package node

import (
	"fmt"
	"sync"
	"time"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/nodetask"
)

type Node struct {
	common.DbNode

	resources *NodeResources

	mu        sync.RWMutex
	remove    *time.Timer
	nodeTasks map[string]*nodetask.NodeTask
}

func NewNode(dbNode common.DbNode) *Node {
	return &Node{
		DbNode:    dbNode,
		resources: NewNodeResources(),
		nodeTasks: make(map[string]*nodetask.NodeTask),
	}
}

func (node *Node) GetDbNode(locks controller.Locks) (dbNode common.DbNode) {
	locks.RLockNode()
	node.mu.RLock()
	dbNode = node.DbNode
	locks.RUnlockNode()
	node.mu.RUnlock()
	return
}

func (node *Node) NodeNumNodeTasks(locks controller.Locks) int {
	locks.RLockNode()
	node.mu.RLock()
	defer node.mu.RUnlock()
	defer locks.RUnlockNode()

	return len(node.nodeTasks)
}

func (node *Node) AddNodeTask(nodeTask *nodetask.NodeTask, locks controller.Locks) {
	dbNodeTask := nodeTask.DbNodeTask()

	locks.LockNode()
	node.mu.Lock()
	node.nodeTasks[dbNodeTask.Id] = nodeTask
	node.resources.AddTask(dbNodeTask)
	locks.UnlockNode()
	node.mu.Unlock()
}

func (node *Node) RemoveNodeTask(dbNodeTask *common.DbNodeTask, locks controller.Locks) {
	locks.LockNode()
	node.mu.Lock()
	delete(node.nodeTasks, dbNodeTask.Id)
	node.resources.RemoveTask(dbNodeTask)
	locks.UnlockNode()
	node.mu.Unlock()
}

func (node *Node) GetNodeTaskList(locks controller.Locks) []*common.DbNodeTask {
	locks.RLockNode()
	node.mu.RLock()
	defer node.mu.RUnlock()
	defer locks.RUnlockNode()

	nodeTasks := make([]*common.DbNodeTask, 0, len(node.nodeTasks))
	controller.AssertLock(locks.Node, controller.LockRLocked)
	for _, nodeTask := range node.nodeTasks {
		nodeTasks = append(nodeTasks, nodeTask.DbNodeTask())
	}

	return nodeTasks
}

func (node *Node) GetNodeTask(dbNodeTask *common.DbNodeTask, locks controller.Locks) *nodetask.NodeTask {
	locks.RLockNode()
	node.mu.RLock()
	defer node.mu.RUnlock()
	defer locks.RUnlockNode()

	return node.nodeTasks[dbNodeTask.Id]
}

func (node *Node) GetAvailableResource(resource string, locks controller.Locks) ([]float64, map[string][]float64) {
	locks.RLockNode()
	node.mu.RLock()
	defer node.mu.RUnlock()
	defer locks.RUnlockNode()
	controller.AssertLock(locks.Node, controller.LockRLocked)
	return node.resources.GetAvailable(resource)
}

// SetDbNode updates the node with the given dbNode and returns true if the node is a zombie.
func (node *Node) SetDbNode(dbNode *common.DbNode, locks controller.Locks) bool {
	locks.LockNode()
	node.mu.Lock()
	defer locks.UnlockNode()
	defer node.mu.Unlock()

	controller.AssertLock(locks.Node, controller.LockLocked)
	enabled := node.Enabled
	capabilities := node.Capabilities

	node.DbNode = *dbNode
	node.Enabled = enabled
	node.Capabilities = capabilities

	if node.remove != nil {
		node.remove.Stop()
		node.remove = nil
	}

	return node.Host == "<zombie>"
}

func (node *Node) Update(enabled bool, capabilities *common.Flags, locks controller.Locks) {
	locks.LockNode()
	node.mu.Lock()
	node.Enabled = enabled
	node.Capabilities = capabilities
	locks.UnlockNode()
	node.mu.Unlock()
}

func (node *Node) Remove(locks controller.Locks) bool {
	locks.LockNode()
	node.mu.Lock()
	defer node.mu.Unlock()
	defer locks.UnlockNode()

	if node.remove == nil {
		return false
	}

	if len(node.nodeTasks) != 0 {
		node.State = "unknown"
	}

	node.remove.Stop()
	node.remove = nil
	return true
}

func (node *Node) SetRemoveTimer(remove *time.Timer, locks controller.Locks) {
	locks.LockNode()
	node.mu.Lock()
	defer node.mu.Unlock()
	defer locks.UnlockNode()

	if node.remove != nil {
		node.remove.Stop()
	}
	node.remove = remove
}

func (node *Node) HasFreeResource(resource string, locks controller.Locks) (ok bool) {
	locks.RLockNode()
	node.mu.RLock()
	_, ok = node.FreeResources[resource] // TODO
	locks.RUnlockNode()
	node.mu.RUnlock()
	return
}

func (node *Node) UpdateResources(free map[string][]float64, available map[string][]float64, locks controller.Locks) {
	locks.LockNode()
	node.mu.Lock()
	defer node.mu.Unlock()
	defer locks.UnlockNode()

	controller.AssertLock(locks.Node, controller.LockLocked)
	node.resources.UpdateResources(free, available)
}

func (node *Node) CheckRequirements(requirements *common.Flags, locks controller.Locks) error {
	if requirements == nil {
		return nil
	}

	for key, value := range *requirements {
		locks.RLockNode()
		node.mu.RLock()
		if node.Capabilities == nil {
			locks.RUnlockNode()
			node.mu.RUnlock()
			return fmt.Errorf("node %s does not have any capabilities", node.Id)
		}

		if (*node.Capabilities)[key] != value {
			locks.RUnlockNode()
			node.mu.RUnlock()
			return fmt.Errorf("node %s does not have capability %s", node.Id, key)
		}
		locks.RUnlockNode()
		node.mu.RUnlock()
	}
	return nil
}

// CanRunTasks checks if the node is enabled and in the 'running' state before
// allowing any tasks to be run on it. It acquires read locks on the node and
// its internal mutex to safely access the state. Returns an error if the node
// is disabled or not running.
func (node *Node) CanRunTasks(locks controller.Locks) error {
	locks.RLockNode()
	node.mu.RLock()
	if !node.Enabled {
		locks.RUnlockNode()
		node.mu.RUnlock()
		return fmt.Errorf("node '%s' is not enabled", node.Id)
	}
	if node.State != "running" {
		locks.RUnlockNode()
		node.mu.RUnlock()
		return fmt.Errorf("node '%s' is not in the 'running' state", node.Id)
	}
	locks.RUnlockNode()
	node.mu.RUnlock()

	return nil
}
