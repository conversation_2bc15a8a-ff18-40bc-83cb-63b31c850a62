package node

import (
	"fmt"
	"math/rand"
	"testing"
	"time"

	"git.moderntv.eu/mcloud/system/common"
)

func BenchmarkAddTask(b *testing.B) {
	scenarios := []struct {
		name      string
		taskCount int
	}{
		{"1000_tasks", 1000},
		{"5000_tasks", 5000},
		{"10000_tasks", 10000},
	}

	for _, scenario := range scenarios {
		b.Run(scenario.name, func(b *testing.B) {
			benchmarkAddTaskWithCount(b, scenario.taskCount)
		})
	}
}

func benchmarkAddTaskWithCount(b *testing.B, existingTaskCount int) {
	// Initialize NodeResources
	nr := NewNodeResources()

	// Set up resources - simulate a node with multiple GPUs
	resources := map[string][]float64{
		"GPU": {1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0}, // 8 GPUs
		"CPU": {100.0, 100.0, 100.0, 100.0},                                     // 4 CPUs
		"RAM": {1000.0, 1000.0},                                                 // 2 RAM slots
	}
	nr.UpdateResources(resources, nil)

	// Pre-populate with existing tasks to simulate real scenario
	existingTasks := make([]*common.DbNodeTask, existingTaskCount)
	for i := 0; i < existingTaskCount; i++ {
		task := createBenchmarkTask(i+1, i%len(resources["GPU"]))
		existingTasks[i] = task
		nr.AddTask(task)
	}

	// Prepare tasks for benchmarking
	benchTasks := make([]*common.DbNodeTask, b.N)
	for i := 0; i < b.N; i++ {
		benchTasks[i] = createBenchmarkTask(existingTaskCount+i+1, i%len(resources["GPU"]))
	}

	// Start benchmark
	b.ResetTimer()
	start := time.Now()

	for i := 0; i < b.N; i++ {
		nr.AddTask(benchTasks[i])
	}

	elapsed := time.Since(start)

	// Calculate and report operations per second
	opsPerSec := float64(b.N) / elapsed.Seconds()
	b.ReportMetric(opsPerSec, "ops/sec")

	// Report if we meet the target of 5000 ops/sec
	if opsPerSec >= 5000 {
		b.Logf("✅ Target achieved: %.2f ops/sec (>= 5000)", opsPerSec)
	} else {
		b.Logf("❌ Target missed: %.2f ops/sec (< 5000)", opsPerSec)
	}
}

func createBenchmarkTask(taskId int, selectedGPU int) *common.DbNodeTask {
	// Create realistic task with resource requirements
	requiredResources := common.Resources{
		"GPU": float64(50 + rand.Intn(200)),  // 50-250 GPU units
		"CPU": float64(10 + rand.Intn(40)),   // 10-50 CPU units
		"RAM": float64(100 + rand.Intn(400)), // 100-500 RAM units
	}

	selectedResources := common.SelectedResources{
		"GPU": selectedGPU,
		"CPU": rand.Intn(4),
		"RAM": rand.Intn(2),
	}

	// Simulate resource usage (partial usage of requirements)
	resourceUsage := make(common.MultiResources)
	for resource, requirement := range requiredResources {
		if resource == "GPU" {
			usage := make([]float64, 8)
			usage[selectedGPU] = requirement * (0.3 + 0.4*rand.Float64()) // 30-70% usage
			resourceUsage[resource] = usage
		} else if resource == "CPU" {
			usage := make([]float64, 4)
			usage[selectedResources[resource]] = requirement * (0.3 + 0.4*rand.Float64())
			resourceUsage[resource] = usage
		} else if resource == "RAM" {
			usage := make([]float64, 2)
			usage[selectedResources[resource]] = requirement * (0.3 + 0.4*rand.Float64())
			resourceUsage[resource] = usage
		}
	}

	// Random task states to simulate real scenarios
	states := []string{"starting", "running", "completed"}
	state := states[rand.Intn(len(states))]

	return &common.DbNodeTask{
		OriginTask: common.RunnableTask{
			DbTask: common.DbTask{
				DbId:              taskId,
				Id:                fmt.Sprintf("task-%d", taskId),
				RequiredResources: &requiredResources,
			},
			SelectedResources: selectedResources,
		},
		State:         state,
		ResourceUsage: resourceUsage,
	}
}

// Benchmark focused on updates (existing tasks)
func BenchmarkAddTaskUpdate(b *testing.B) {
	scenarios := []struct {
		name      string
		taskCount int
	}{
		{"1000_tasks_update", 1000},
		{"5000_tasks_update", 5000},
		{"10000_tasks_update", 10000},
	}

	for _, scenario := range scenarios {
		b.Run(scenario.name, func(b *testing.B) {
			benchmarkAddTaskUpdateWithCount(b, scenario.taskCount)
		})
	}
}

func benchmarkAddTaskUpdateWithCount(b *testing.B, taskCount int) {
	// Initialize NodeResources
	nr := NewNodeResources()

	// Set up resources
	resources := map[string][]float64{
		"GPU": {1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0, 1000.0},
		"CPU": {100.0, 100.0, 100.0, 100.0},
		"RAM": {1000.0, 1000.0},
	}
	nr.UpdateResources(resources, nil)

	// Pre-populate with tasks
	tasks := make([]*common.DbNodeTask, taskCount)
	for i := 0; i < taskCount; i++ {
		task := createBenchmarkTask(i+1, i%8)
		tasks[i] = task
		nr.AddTask(task)
	}

	// Prepare updated tasks for benchmarking
	benchTasks := make([]*common.DbNodeTask, b.N)
	for i := 0; i < b.N; i++ {
		// Update existing task (simulate resource usage change)
		originalTask := tasks[i%taskCount]
		updatedTask := createBenchmarkTask(originalTask.OriginTask.DbId, originalTask.OriginTask.SelectedResources["GPU"])
		benchTasks[i] = updatedTask
	}

	// Start benchmark
	b.ResetTimer()
	start := time.Now()

	for i := 0; i < b.N; i++ {
		nr.AddTask(benchTasks[i])
	}

	elapsed := time.Since(start)

	// Calculate and report operations per second
	opsPerSec := float64(b.N) / elapsed.Seconds()
	b.ReportMetric(opsPerSec, "ops/sec")

	// Report if we meet the target of 5000 ops/sec
	if opsPerSec >= 5000 {
		b.Logf("✅ Target achieved: %.2f ops/sec (>= 5000)", opsPerSec)
	} else {
		b.Logf("❌ Target missed: %.2f ops/sec (< 5000)", opsPerSec)
	}
}
