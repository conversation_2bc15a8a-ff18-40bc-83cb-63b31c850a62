package node

import (
	"time"

	"git.moderntv.eu/mcloud/system/common"
)

func ReservedResources(dbNodeTask *common.DbNodeTask) bool {
	if dbNodeTask.LastOnlineTime == nil {
		return true
	}
	if dbNodeTask.State == "dummy" || dbNodeTask.State == "initializing" || dbNodeTask.State == "starting" {
		return true
	}
	if dbNodeTask.State == "running" {
		ref := *dbNodeTask.LastOnlineTime

		//if dbNodeTask.StartTime != nil && ref.Sub(*dbNodeTask.StartTime) < 30*time.Second { //TODO constant
		//	return true
		//}
		if dbNodeTask.RunningSinceTime == nil || ref.Sub(*dbNodeTask.RunningSinceTime) < 15*time.Second { //TODO constant
			return true
		}
	}

	return false
}
