package node

import (
	"git.moderntv.eu/mcloud/system/common"
)

// TaskResourceInfo holds information about resource usage for a specific task
type TaskResourceInfo struct {
	Reserved []float64 // Amount reserved for this task (immediate allocation)
	Reserve  []float64 // Amount that could be reserved (potential maximum usage)
}

// SliceManager handles consistent slice operations across all components
type SliceManager struct{}

// NewSliceManager creates a new slice manager
func NewSliceManager() *SliceManager {
	return &SliceManager{}
}

// EnsureSliceSize ensures slice has the correct size, initializing new elements to zero
func (sm *SliceManager) EnsureSliceSize(slice *[]float64, size int) {
	if len(*slice) != size {
		if cap(*slice) >= size {
			oldLen := len(*slice)
			*slice = (*slice)[:size]
			// Clear newly visible elements
			for i := oldLen; i < size; i++ {
				(*slice)[i] = 0
			}
		} else {
			newSlice := make([]float64, size)
			copy(newSlice, *slice)
			*slice = newSlice
		}
	}
}

// InitializeSlice creates a new slice with the specified size, all elements set to zero
func (sm *SliceManager) InitializeSlice(size int) []float64 {
	return make([]float64, size)
}

// CopySlice creates a deep copy of the input slice
func (sm *SliceManager) CopySlice(src []float64) []float64 {
	if src == nil {
		return nil
	}
	dst := make([]float64, len(src))
	copy(dst, src)
	return dst
}

// ReservationManager handles immediate resource reservations
type ReservationManager struct {
	sliceManager    *SliceManager
	totalReserved   map[string][]float64
	reservationInfo map[string]map[string][]float64
}

// NewReservationManager creates a new reservation manager
func NewReservationManager(sliceManager *SliceManager) *ReservationManager {
	return &ReservationManager{
		sliceManager:    sliceManager,
		totalReserved:   make(map[string][]float64),
		reservationInfo: make(map[string]map[string][]float64),
	}
}

// InitializeResource initializes reservation tracking for a resource type
func (rm *ReservationManager) InitializeResource(resourceType string, size int) {
	if rm.totalReserved[resourceType] == nil {
		rm.totalReserved[resourceType] = rm.sliceManager.InitializeSlice(size)
	} else {
		slice := rm.totalReserved[resourceType]
		rm.sliceManager.EnsureSliceSize(&slice, size)
		rm.totalReserved[resourceType] = slice
	}

	if rm.reservationInfo[resourceType] == nil {
		rm.reservationInfo[resourceType] = make(map[string][]float64)
	}
}

// AddReservation adds a reservation for a specific task
func (rm *ReservationManager) AddReservation(resourceType, taskID string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	// Update total reserved
	totalSlice := rm.totalReserved[resourceType]
	for i, reservation := range reservations {
		if i < len(totalSlice) && reservation > 0 {
			totalSlice[i] += reservation
		}
	}

	// Update reservation info
	taskSlice := rm.reservationInfo[resourceType][taskID]
	rm.sliceManager.EnsureSliceSize(&taskSlice, len(totalSlice))
	rm.reservationInfo[resourceType][taskID] = taskSlice
	copy(taskSlice, reservations)
}

// RemoveReservation removes a reservation for a specific task
func (rm *ReservationManager) RemoveReservation(resourceType, taskID string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	// Update total reserved
	totalSlice := rm.totalReserved[resourceType]
	for i, reservation := range reservations {
		if i < len(totalSlice) && reservation > 0 {
			totalSlice[i] -= reservation
		}
	}

	// Remove from reservation info
	if rm.reservationInfo[resourceType] != nil {
		delete(rm.reservationInfo[resourceType], taskID)
	}
}

// GetTotalReserved returns a copy of total reserved resources
func (rm *ReservationManager) GetTotalReserved() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range rm.totalReserved {
		result[res] = rm.sliceManager.CopySlice(values)
	}
	return result
}

// GetReservationInfo returns reservation info for a specific resource
func (rm *ReservationManager) GetReservationInfo(resourceType string) map[string][]float64 {
	return rm.reservationInfo[resourceType]
}

// SetNodeReservation sets node-level reservation (difference between free and available)
func (rm *ReservationManager) SetNodeReservation(resourceType string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	nodeSlice := rm.reservationInfo[resourceType]["(node)"]
	rm.sliceManager.EnsureSliceSize(&nodeSlice, len(reservations))
	rm.reservationInfo[resourceType]["(node)"] = nodeSlice
	copy(nodeSlice, reservations)
}

// PotentialReserveManager handles maximum potential reserves for non-reserved tasks
type PotentialReserveManager struct {
	sliceManager *SliceManager
	maxReserve   map[string][]float64
	taskReserves map[int]map[string][]float64 // taskID -> resourceType -> reserves
}

// NewPotentialReserveManager creates a new potential reserve manager
func NewPotentialReserveManager(sliceManager *SliceManager) *PotentialReserveManager {
	return &PotentialReserveManager{
		sliceManager: sliceManager,
		maxReserve:   make(map[string][]float64),
		taskReserves: make(map[int]map[string][]float64),
	}
}

// InitializeResource initializes max reserve tracking for a resource type
func (prm *PotentialReserveManager) InitializeResource(resourceType string, size int) {
	if prm.maxReserve[resourceType] == nil {
		prm.maxReserve[resourceType] = prm.sliceManager.InitializeSlice(size)
	} else {
		slice := prm.maxReserve[resourceType]
		prm.sliceManager.EnsureSliceSize(&slice, size)
		prm.maxReserve[resourceType] = slice
	}
}

// AddTaskReserve adds potential reserves for a task and updates max reserves incrementally
func (prm *PotentialReserveManager) AddTaskReserve(taskID int, resourceType string, reserves []float64) {
	if len(reserves) == 0 {
		return
	}

	// Initialize task reserves if needed
	if prm.taskReserves[taskID] == nil {
		prm.taskReserves[taskID] = make(map[string][]float64)
	}

	// Store task reserves
	prm.taskReserves[taskID][resourceType] = prm.sliceManager.CopySlice(reserves)

	// Update max reserves incrementally
	maxSlice := prm.maxReserve[resourceType]
	for i, reserve := range reserves {
		if i < len(maxSlice) && reserve > maxSlice[i] {
			maxSlice[i] = reserve
		}
	}
}

// RemoveTaskReserve removes potential reserves for a task
func (prm *PotentialReserveManager) RemoveTaskReserve(taskID int, resourceType string) bool {
	if prm.taskReserves[taskID] == nil {
		return false
	}

	reserves := prm.taskReserves[taskID][resourceType]
	if reserves == nil {
		return false
	}

	// Check if this task had the maximum reserve - if so, we need to recalculate
	needsRecalc := false
	maxSlice := prm.maxReserve[resourceType]
	for i, reserve := range reserves {
		if i < len(maxSlice) && reserve > 0 && reserve >= maxSlice[i] {
			needsRecalc = true
			break
		}
	}

	// Remove task reserves
	delete(prm.taskReserves[taskID], resourceType)
	if len(prm.taskReserves[taskID]) == 0 {
		delete(prm.taskReserves, taskID)
	}

	return needsRecalc
}

// RecalculateMaxReserveForResource recalculates max reserve for a specific resource
func (prm *PotentialReserveManager) RecalculateMaxReserveForResource(resourceType string) {
	maxSlice := prm.maxReserve[resourceType]
	if maxSlice == nil {
		return
	}

	// Reset max reserves for this resource
	for i := range maxSlice {
		maxSlice[i] = 0
	}

	// Recalculate from all tasks for this resource only
	for _, taskResources := range prm.taskReserves {
		reserves := taskResources[resourceType]
		if reserves != nil {
			for i, val := range reserves {
				if i < len(maxSlice) && val > maxSlice[i] {
					maxSlice[i] = val
				}
			}
		}
	}
}

// GetMaxReserve returns a copy of max reserves
func (prm *PotentialReserveManager) GetMaxReserve() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range prm.maxReserve {
		result[res] = prm.sliceManager.CopySlice(values)
	}
	return result
}

// GetMaxReserveForResource returns max reserve for a specific resource
func (prm *PotentialReserveManager) GetMaxReserveForResource(resourceType string) []float64 {
	return prm.maxReserve[resourceType]
}

// NodeResources is the main coordinator that maintains API compatibility
type NodeResources struct {
	// Total available resources on the node
	totalResources map[string][]float64

	// Component managers
	sliceManager            *SliceManager
	reservationManager      *ReservationManager
	potentialReserveManager *PotentialReserveManager

	// Map of task ID -> resource type -> resource info (for API compatibility)
	tasks map[int]map[string]*TaskResourceInfo
}

func NewNodeResources() *NodeResources {
	sliceManager := NewSliceManager()
	return &NodeResources{
		totalResources:          make(map[string][]float64),
		sliceManager:            sliceManager,
		reservationManager:      NewReservationManager(sliceManager),
		potentialReserveManager: NewPotentialReserveManager(sliceManager),
		tasks:                   make(map[int]map[string]*TaskResourceInfo),
	}
}

func (nr *NodeResources) UpdateResources(free map[string][]float64, available map[string][]float64) {
	if free == nil {
		return
	}

	// Use available if provided, otherwise use free
	resources := free
	if available != nil {
		resources = available
	}

	// Update total resources and initialize managers
	for res, values := range resources {
		// Update total resources
		nr.totalResources[res] = nr.sliceManager.CopySlice(values)

		// Initialize managers for this resource type
		nr.reservationManager.InitializeResource(res, len(values))
		nr.potentialReserveManager.InitializeResource(res, len(values))

		// Handle node reservation (difference between free and available)
		if available != nil {
			freeVals := free[res]
			nodeReservations := make([]float64, len(values))
			hasNodeReservation := false

			for i, availVal := range values {
				if i < len(freeVals) {
					nodeReservation := freeVals[i] - availVal
					if nodeReservation > 0 {
						nodeReservations[i] = nodeReservation
						hasNodeReservation = true
					}
				}
			}

			if hasNodeReservation {
				nr.reservationManager.SetNodeReservation(res, nodeReservations)
			}
		}
	}
}

func (nr *NodeResources) AddTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	// Remove task first if it exists (update operation)
	nr.RemoveTask(dbNodeTask)

	required := dbNodeTask.OriginTask.RequiredResources
	if required == nil {
		return
	}

	// Initialize task map
	if nr.tasks[taskId] == nil {
		nr.tasks[taskId] = make(map[string]*TaskResourceInfo)
	}

	isReserved := ReservedResources(dbNodeTask)
	selectedRes := dbNodeTask.OriginTask.SelectedResources
	taskStringId := dbNodeTask.OriginTask.Id

	for res, reqValue := range *required {
		if reqValue == 0 {
			continue
		}

		// Get resource dimensions
		totalSlice := nr.totalResources[res]
		if totalSlice == nil {
			continue
		}
		l := len(totalSlice)

		// Initialize task resource info
		taskInfo := &TaskResourceInfo{
			Reserved: nr.sliceManager.InitializeSlice(l),
			Reserve:  nr.sliceManager.InitializeSlice(l),
		}

		// Calculate resource requirements
		usage := dbNodeTask.ResourceUsage[res]
		if selectedRes != nil {
			if i, has := selectedRes[res]; has && i < l {
				// Calculate reserve amount (required - used)
				var reserveAmount float64
				if len(usage) == l && usage[i] > 0 {
					reserveAmount = reqValue - usage[i]
				} else {
					reserveAmount = reqValue
				}

				if reserveAmount > 0 {
					if isReserved {
						// For reserved tasks, add immediate reservation
						taskInfo.Reserved[i] = reserveAmount
						reservations := nr.sliceManager.InitializeSlice(l)
						reservations[i] = reserveAmount
						nr.reservationManager.AddReservation(res, taskStringId, reservations)
					} else {
						// For non-reserved tasks, add potential reserve
						taskInfo.Reserve[i] = reserveAmount
						reserves := nr.sliceManager.InitializeSlice(l)
						reserves[i] = reserveAmount
						nr.potentialReserveManager.AddTaskReserve(taskId, res, reserves)
					}
				}
			}
		}

		nr.tasks[taskId][res] = taskInfo
	}
}

func (nr *NodeResources) RemoveTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	taskResources, exists := nr.tasks[taskId]
	if !exists {
		return
	}

	taskStringId := dbNodeTask.OriginTask.Id

	// Remove task's resource usage from managers
	for res, taskInfo := range taskResources {
		if taskInfo == nil {
			continue
		}

		// Remove reserved resources
		if hasReservations(taskInfo.Reserved) {
			nr.reservationManager.RemoveReservation(res, taskStringId, taskInfo.Reserved)
		}

		// Remove potential reserves and check if recalculation is needed
		if hasReservations(taskInfo.Reserve) {
			needsRecalc := nr.potentialReserveManager.RemoveTaskReserve(taskId, res)
			if needsRecalc {
				nr.potentialReserveManager.RecalculateMaxReserveForResource(res)
			}
		}
	}

	delete(nr.tasks, taskId)
}

// hasReservations checks if a slice has any non-zero values
func hasReservations(slice []float64) bool {
	for _, val := range slice {
		if val > 0 {
			return true
		}
	}
	return false
}

func (nr *NodeResources) GetAvailable(resource string) ([]float64, map[string][]float64) {
	totalSlice := nr.totalResources[resource]
	if totalSlice == nil {
		return nil, nil
	}

	// Get reserved and max reserve slices
	totalReserved := nr.reservationManager.GetTotalReserved()[resource]
	maxReserve := nr.potentialReserveManager.GetMaxReserveForResource(resource)

	// Calculate available = total - reserved - maxReserve
	available := make([]float64, len(totalSlice))
	for i := range totalSlice {
		avail := totalSlice[i]

		// Subtract immediate reservations
		if totalReserved != nil && i < len(totalReserved) {
			avail -= totalReserved[i]
		}

		// Subtract maximum reserve
		if maxReserve != nil && i < len(maxReserve) {
			avail -= maxReserve[i]
		}

		if avail < 0 {
			available[i] = 0
		} else {
			available[i] = avail
		}
	}

	return available, nr.reservationManager.GetReservationInfo(resource)
}

// GetTaskResourceUsage returns resource usage for a specific task
func (nr *NodeResources) GetTaskResourceUsage(taskId int) map[string]*TaskResourceInfo {
	return nr.tasks[taskId]
}

// GetTotalReserved returns total reserved resources
func (nr *NodeResources) GetTotalReserved() map[string][]float64 {
	return nr.reservationManager.GetTotalReserved()
}

// GetMaxReserve returns maximum reserves
func (nr *NodeResources) GetMaxReserve() map[string][]float64 {
	return nr.potentialReserveManager.GetMaxReserve()
}
