package node

import (
	"git.moderntv.eu/mcloud/system/common"
)

// TaskResourceInfo holds information about resource usage for a specific task
type TaskResourceInfo struct {
	Reserved []float64 // Amount reserved for this task (immediate allocation)
	Reserve  []float64 // Amount that could be reserved (potential maximum usage)
}

// NodeResources simplified structure that tracks only essential information
type NodeResources struct {
	// Total available resources on the node
	totalResources map[string][]float64

	// Total reserved resources per resource type (immediately allocated)
	totalReserved map[string][]float64

	// Maximum reserve per resource type (highest potential reserve among all tasks)
	maxReserve map[string][]float64

	// Map of task ID -> resource type -> resource info
	tasks map[int]map[string]*TaskResourceInfo

	// Reservation info for reporting
	reservationInfo map[string]map[string][]float64
}

func NewNodeResources() *NodeResources {
	return &NodeResources{
		totalResources:  make(map[string][]float64),
		totalReserved:   make(map[string][]float64),
		maxReserve:      make(map[string][]float64),
		tasks:           make(map[int]map[string]*TaskResourceInfo),
		reservationInfo: make(map[string]map[string][]float64),
	}
}

func (nr *NodeResources) UpdateResources(free map[string][]float64, available map[string][]float64) {
	if free == nil {
		return
	}

	// Use available if provided, otherwise use free
	resources := free
	if available != nil {
		resources = available
	}

	// Update total resources and ensure all slices exist
	for res, values := range resources {
		// Update total resources
		nr.totalResources[res] = make([]float64, len(values))
		copy(nr.totalResources[res], values)

		// Initialize counters if they don't exist
		if nr.totalReserved[res] == nil {
			nr.totalReserved[res] = make([]float64, len(values))
		}
		if nr.maxReserve[res] == nil {
			nr.maxReserve[res] = make([]float64, len(values))
		}

		// Ensure slice sizes match
		reservedSlice := nr.totalReserved[res]
		nr.ensureSliceSize(&reservedSlice, len(values))
		nr.totalReserved[res] = reservedSlice

		maxReserveSlice := nr.maxReserve[res]
		nr.ensureSliceSize(&maxReserveSlice, len(values))
		nr.maxReserve[res] = maxReserveSlice

		// Handle node reservation (difference between free and available)
		if available != nil {
			freeVals := free[res]
			for i, availVal := range values {
				if i < len(freeVals) {
					nodeReservation := freeVals[i] - availVal
					if nodeReservation > 0 {
						if nr.reservationInfo[res] == nil {
							nr.reservationInfo[res] = make(map[string][]float64)
						}
						nodeSlice := nr.reservationInfo[res]["(node)"]
						nr.ensureSliceSize(&nodeSlice, len(values))
						nr.reservationInfo[res]["(node)"] = nodeSlice
						nodeSlice[i] = nodeReservation
					}
				}
			}
		}
	}
}

func (nr *NodeResources) AddTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	// Remove task first if it exists (update operation)
	nr.RemoveTask(dbNodeTask)

	required := dbNodeTask.OriginTask.RequiredResources
	if required == nil {
		return
	}

	// Initialize task map
	if nr.tasks[taskId] == nil {
		nr.tasks[taskId] = make(map[string]*TaskResourceInfo)
	}

	isReserved := ReservedResources(dbNodeTask)
	selectedRes := dbNodeTask.OriginTask.SelectedResources
	taskStringId := dbNodeTask.OriginTask.Id

	for res, reqValue := range *required {
		if reqValue == 0 {
			continue
		}

		// Get resource dimensions
		totalSlice := nr.totalResources[res]
		if totalSlice == nil {
			continue
		}
		l := len(totalSlice)

		// Initialize task resource info
		taskInfo := &TaskResourceInfo{
			Reserved: make([]float64, l),
			Reserve:  make([]float64, l),
		}

		// Initialize reservation info
		if nr.reservationInfo[res] == nil {
			nr.reservationInfo[res] = make(map[string][]float64)
		}
		taskInfoSlice := nr.reservationInfo[res][taskStringId]
		nr.ensureSliceSize(&taskInfoSlice, l)
		nr.reservationInfo[res][taskStringId] = taskInfoSlice

		// All tasks use reserve logic (required - used), regardless of reserved status
		// The difference is that reserved tasks have their reserves counted immediately,
		// while non-reserved tasks contribute to maxReserve calculation
		usage := dbNodeTask.ResourceUsage[res]
		if selectedRes != nil {
			if i, has := selectedRes[res]; has && i < l {
				// Set reserve only at the selected index
				var reserveAmount float64
				if len(usage) == l && usage[i] > 0 {
					// Reserve = required - used
					reserveAmount = reqValue - usage[i]
				} else {
					// No usage data, reserve full requirement
					reserveAmount = reqValue
				}

				if reserveAmount > 0 {
					if isReserved {
						// For reserved tasks, count the reserve as immediate reservation
						taskInfo.Reserved[i] = reserveAmount
						nr.totalReserved[res][i] += reserveAmount
						taskInfoSlice[i] = reserveAmount
					} else {
						// For non-reserved tasks, set the reserve and update maxReserve incrementally
						taskInfo.Reserve[i] = reserveAmount
						// Update maxReserve only if this reserve is higher
						if nr.maxReserve[res][i] < reserveAmount {
							nr.maxReserve[res][i] = reserveAmount
						}
					}
				}
			}
		}

		nr.tasks[taskId][res] = taskInfo
	}

	// No need to recalculate all maxReserves - we updated them incrementally
}

func (nr *NodeResources) RemoveTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	taskResources, exists := nr.tasks[taskId]
	if !exists {
		return
	}

	// Remove from reservation info
	taskStringId := dbNodeTask.OriginTask.Id
	for _, tasks := range nr.reservationInfo {
		delete(tasks, taskStringId)
	}

	// Track if we need to recalculate maxReserves for specific resources
	needsRecalc := make(map[string]bool)

	// Subtract task's resource usage from totals
	for res, taskInfo := range taskResources {
		if taskInfo == nil {
			continue
		}

		// Subtract reserved resources
		for i, reserved := range taskInfo.Reserved {
			if reserved > 0 {
				nr.totalReserved[res][i] -= reserved
			}
		}

		// Check if this task had the maximum reserve - if so, we need to recalculate
		for i, reserve := range taskInfo.Reserve {
			if reserve > 0 && reserve >= nr.maxReserve[res][i] {
				needsRecalc[res] = true
				break
			}
		}
	}

	delete(nr.tasks, taskId)

	// Only recalculate maxReserves for resources that actually need it
	for res := range needsRecalc {
		nr.recalculateMaxReserveForResource(res)
	}
}

func (nr *NodeResources) GetAvailable(resource string) ([]float64, map[string][]float64) {
	totalSlice := nr.totalResources[resource]
	if totalSlice == nil {
		return nil, nil
	}

	// Calculate available = total - reserved - maxReserve
	available := make([]float64, len(totalSlice))
	for i := range totalSlice {
		avail := totalSlice[i]

		// Subtract immediate reservations
		if nr.totalReserved[resource] != nil && i < len(nr.totalReserved[resource]) {
			avail -= nr.totalReserved[resource][i]
		}

		// Subtract maximum reserve
		if nr.maxReserve[resource] != nil && i < len(nr.maxReserve[resource]) {
			avail -= nr.maxReserve[resource][i]
		}

		if avail < 0 {
			available[i] = 0
		} else {
			available[i] = avail
		}
	}

	return available, nr.reservationInfo[resource]
}

// Helper function to ensure slice has the correct size
func (nr *NodeResources) ensureSliceSize(slice *[]float64, size int) {
	if len(*slice) != size {
		if cap(*slice) >= size {
			oldLen := len(*slice)
			*slice = (*slice)[:size]
			// Clear newly visible elements
			for i := oldLen; i < size; i++ {
				(*slice)[i] = 0
			}
		} else {
			newSlice := make([]float64, size)
			copy(newSlice, *slice)
			*slice = newSlice
		}
	}
}

func (nr *NodeResources) updateMaxReserve(res string, reserve []float64) {
	if reserve == nil {
		return
	}

	maxReserveSlice := nr.maxReserve[res]
	for i, val := range reserve {
		if i < len(maxReserveSlice) && val > maxReserveSlice[i] {
			maxReserveSlice[i] = val
		}
	}
}

func (nr *NodeResources) recalculateMaxReserves() {
	// Reset all maxReserves to 0
	for res := range nr.maxReserve {
		for i := range nr.maxReserve[res] {
			nr.maxReserve[res][i] = 0
		}
	}

	// Recalculate from all non-reserved tasks
	for _, taskResources := range nr.tasks {
		for res, taskInfo := range taskResources {
			if taskInfo != nil && taskInfo.Reserve != nil {
				// Only update max reserve if this task is not reserved (has no immediate reservations)
				hasReservation := false
				for _, reserved := range taskInfo.Reserved {
					if reserved > 0 {
						hasReservation = true
						break
					}
				}
				if !hasReservation {
					nr.updateMaxReserve(res, taskInfo.Reserve)
				}
			}
		}
	}
}

// New optimized method that recalculates maxReserve only for one resource
func (nr *NodeResources) recalculateMaxReserveForResource(res string) {
	maxReserveSlice := nr.maxReserve[res]
	if maxReserveSlice == nil {
		return
	}

	// Reset maxReserve for this resource
	for i := range maxReserveSlice {
		maxReserveSlice[i] = 0
	}

	// Recalculate from all non-reserved tasks for this resource only
	for _, taskResources := range nr.tasks {
		taskInfo := taskResources[res]
		if taskInfo != nil && taskInfo.Reserve != nil {
			// Only update max reserve if this task is not reserved (has no immediate reservations)
			hasReservation := false
			for _, reserved := range taskInfo.Reserved {
				if reserved > 0 {
					hasReservation = true
					break
				}
			}
			if !hasReservation {
				for i, val := range taskInfo.Reserve {
					if i < len(maxReserveSlice) && val > maxReserveSlice[i] {
						maxReserveSlice[i] = val
					}
				}
			}
		}
	}
}

// GetTaskResourceUsage returns resource usage for a specific task
func (nr *NodeResources) GetTaskResourceUsage(taskId int) map[string]*TaskResourceInfo {
	return nr.tasks[taskId]
}

// GetTotalReserved returns total reserved resources
func (nr *NodeResources) GetTotalReserved() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range nr.totalReserved {
		result[res] = make([]float64, len(values))
		copy(result[res], values)
	}
	return result
}

// GetMaxReserve returns maximum reserves
func (nr *NodeResources) GetMaxReserve() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range nr.maxReserve {
		result[res] = make([]float64, len(values))
		copy(result[res], values)
	}
	return result
}
