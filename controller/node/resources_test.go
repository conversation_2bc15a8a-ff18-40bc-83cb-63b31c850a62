package node

import (
	"testing"
	"time"

	"github.com/matryer/is"

	"git.moderntv.eu/mcloud/system/common"
)

func TestNodeResources(t *testing.T) {
	// return // TODO: remove, currently crashes vscode
	t.Parallel()
	is := is.New(t)

	var now = time.Now()
	var startTime = now.Add(-10 * time.Minute)

	var dbTask1 = common.DbTask{
		DbId:              1,
		Id:                "task1",
		Action:            "run",
		RequiredResources: &common.Resources{"GPU": 500.0},
	}
	var dbTask2 = common.DbTask{
		DbId:              2,
		Id:                "task2",
		Action:            "run",
		RequiredResources: &common.Resources{"GPU": 300.0},
	}

	var sr = common.NewSelectedResources()
	sr["GPU"] = 1
	sr["disk"] = 0

	t.Run("basic", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})

	t.Run("starting node task", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:             "nodetask1",
			Task:           dbTask1.DbId,
			Node:           "node1",
			State:          "starting",
			StartTime:      &startTime,
			LastOnlineTime: &now,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		dbNodeTask2 := common.DbNodeTask{
			Id:             "nodetask2",
			Task:           dbTask2.DbId,
			Node:           "node1",
			State:          "starting",
			LastOnlineTime: &now,
			StartTime:      &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 350.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("running node task", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:               "nodetask1",
			Task:             dbTask1.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("starting to running node task", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask1.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)

		dbNodeTask2 := common.DbNodeTask{
			Id:    "nodetask2",
			Task:  dbTask2.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)

		dbNodeTask1 = common.DbNodeTask{
			Id:               "nodetask1",
			Task:             dbTask1.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		dbNodeTask2 = common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("maximum task reservation reserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 500.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:               "nodetask1",
			Task:             dbTask1.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 1.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)

		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1.0, 1000.0})

		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 1.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)

		//avail, _ = nr.GetAvailable("GPU")
		//is.Equal(avail, []float64{1000.0, 0.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})
	})
	t.Run("change free resources", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 500.0, 1000.0}}, nil)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})

		dbNodeTask1 := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask1.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}}, // out of 500
		}
		nr.AddTask(&dbNodeTask1)
		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}}, // out of 300
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 0.0, 1000.0})

		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 350.0, 1000.0})
	})
	t.Run("node reservation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, map[string][]float64{"GPU": []float64{1000.0, 500.0, 1000.0}})
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})

		dbNodeTask1 := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask1.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 100.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})

		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}})
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("multiple updates", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		for i := 0; i < 100000; i++ {
			valNode := float64(((i * 5) % 500) + 10)
			valTaskX := float64((i*51)%1000) + 50
			valTaskY := float64((i*33)%1000) + 7

			t.Logf("updating node: free=%v, available=%v\n", map[string][]float64{"GPU": []float64{0, valNode + 50}}, map[string][]float64{"GPU": []float64{0, valNode}})
			nr.UpdateResources(map[string][]float64{"GPU": []float64{0, valNode + 50}}, map[string][]float64{"GPU": []float64{0, valNode}})

			var dbTaskX = common.DbTask{
				DbId:              1,
				Id:                "taskX",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": valTaskX},
			}
			dbNodeTaskX := common.DbNodeTask{
				Id:             "nodetaskX",
				Task:           dbTaskX.DbId,
				Node:           "node",
				State:          "starting",
				LastOnlineTime: &now,
				StartTime:      &startTime,
				OriginTask: common.RunnableTask{
					DbTask:            dbTaskX,
					SelectedResources: sr,
				},
				ResourceUsage: common.MultiResources{"GPU": []float64{0, valTaskX / 2}},
			}
			t.Logf("adding starting task: required=%f, used=%v\n", valTaskX, common.MultiResources{"GPU": []float64{0, valTaskX / 2}})
			nr.AddTask(&dbNodeTaskX)

			var dbTaskY = common.DbTask{
				DbId:              2,
				Id:                "taskY",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": valTaskY},
			}
			dbNodeTaskY := common.DbNodeTask{
				Id:               "nodetaskY",
				Task:             dbTaskY.DbId,
				Node:             "node",
				State:            "running",
				LastOnlineTime:   &now,
				LastRunningTime:  &now,
				StartTime:        &startTime,
				RunningSinceTime: &startTime,
				OriginTask: common.RunnableTask{
					DbTask:            dbTaskY,
					SelectedResources: sr,
				},
				ResourceUsage: common.MultiResources{"GPU": []float64{0, valTaskY / 2}},
			}
			t.Logf("adding running task: required=%f, used=%v\n", valTaskX, common.MultiResources{"GPU": []float64{0, valTaskX / 2}})
			nr.AddTask(&dbNodeTaskY)

			avail, _ := nr.GetAvailable("GPU")
			expected := valNode - valTaskX/2 - valTaskY/2
			if expected < 0 {
				expected = 0
			}
			is.Equal(avail, []float64{0, expected})
		}
	})
	t.Run("add and remove resource index", func(t *testing.T) {
		//TODO
	})

	//TODO a task with more resources keys than node
}
