package runningcontroller

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"reflect"
	"runtime"
	"sync"
	"time"

	gocommon "git.moderntv.eu/go/common"
	"git.moderntv.eu/go/counter"
	"git.moderntv.eu/mcloud/log"
	mNats "git.moderntv.eu/mcloud/nats"
	storage "git.moderntv.eu/mcloud/storage/data"
	"github.com/nats-io/nats.go"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/cloud"
	"git.moderntv.eu/mcloud/system/controller/db"
	"git.moderntv.eu/mcloud/system/controller/task"
)

const AutoPriority = -1
const (
	StateInit    = "initializing"
	StateIdle    = "idle"
	StateMaster  = "master"
	StateStopped = "stopped"
	StateError   = "error"
)

type RunningController struct {
	dbController common.DbController // TODO remove

	version string
	cloud   *cloud.Cloud

	Db        *db.DB
	dbBump    chan bool
	dbQueries chan AsyncFunction
	enc       *nats.EncodedConn
	closed    bool

	cpuTotalCounter *counter.Counter
	cpuIdleCounter  *counter.Counter
	httpClient      *http.Client
	apiUrl          string

	FatalErrors chan error
	stateSender *mNats.StateSender

	mu               sync.RWMutex
	lastDbWrite      time.Time
	quorum           bool
	workersForTasks  int
	finishedTasksSet map[string]bool

	metricsHandler http.Handler
	metrics        *controller.Metrics
}

func New(hostname string, version string, cloud *cloud.Cloud, db *db.DB, nc *nats.Conn, metrics *controller.Metrics, apiUrl string, priority int, quorum int, workersForTasks int) (myController *RunningController, err error) {
	var enc *nats.EncodedConn
	enc, err = nats.NewEncodedConn(nc, nats.JSON_ENCODER)
	if err != nil {
		return
	}
	myController = &RunningController{
		dbController: common.DbController{
			Id:     "controller_" + hostname,
			Type:   "controller",
			Host:   hostname,
			State_: StateInit,
		},

		version:   version,
		cloud:     cloud,
		Db:        db,
		enc:       enc,
		dbBump:    make(chan bool, 1),
		dbQueries: make(chan AsyncFunction, 1024), // TODO constant

		cpuTotalCounter: counter.Create(counter.SourceFunc(gocommon.GetCPUTotal), 1*time.Second, 30, false),
		cpuIdleCounter:  counter.Create(counter.SourceFunc(gocommon.GetCPUIdle), 1*time.Second, 30, false),

		httpClient: &http.Client{
			Timeout: time.Duration(10) * time.Second, // TODO constant
		},
		apiUrl:           apiUrl,
		FatalErrors:      make(chan error, 1),
		finishedTasksSet: make(map[string]bool, 1024),
		metricsHandler:   promhttp.Handler(),
		metrics:          metrics,

		workersForTasks: workersForTasks,
	}

	nc.SetReconnectHandler(func(*nats.Conn) {
		locks := controller.NewLocks()
		locks.LockController()
		myController.mu.Lock()
		if myController.state(StateMaster) {
			myController.Log(log.LOG_DEBUG, "dropping master status because of NATS reconnection")
			myController.setState(StateIdle)
		}
		locks.UnlockController()
		myController.mu.Unlock()
	})

	myController.stateSender, err = mNats.NewStateSender(enc, myController.getState, "controller_state."+mNats.EscapeRoutingKey(myController.dbController.Host), common.ControllerStateInterval)
	if err != nil {
		return
	}
	locks := controller.NewLocks()
	myController.statusReceiver(locks)

	go myController.prioritize(priority)
	go myController.asyncHandler(myController.dbQueries)
	go myController.dispatcher()
	if quorum > 0 {
		go myController.quorumMonitor(quorum)
	}

	go func() {
		if db == nil {
			return
		}
		myController.Db.Connect()

		ticker := time.NewTicker(3 * time.Second) // TODO constant
		defer ticker.Stop()

		locks := controller.NewLocks()

		var err error
		var now time.Time
		for {
			now = <-ticker.C

			err = myController.DbPushSelf()
			if err != nil {
				myController.Log(log.LOG_ERROR, "dbPushSelf ERROR: %s", err)
			} else {
				myController.lastDbWrite = now
			}
			if myController.State(StateMaster, locks) {
				err = myController.DbPush(locks)
				if err != nil {
					myController.Log(log.LOG_ERROR, "dbPush ERROR: %s", err)
				}
			}
		}
	}()
	go func() {
		if db == nil {
			return
		}
		myController.Db.Connect()
		/* start updating from db after a delay to get all info through messaging first */
		time.Sleep(10 * time.Second) // TODO constant

		locks := controller.NewLocks()
		myController.dbUpdater(locks)
		panic("dbUpdater exited")
	}()

	myController.Log(log.LOG_INFO, "running")

	return
}

func (control *RunningController) Log(logLevel log.LogLevel, format string, args ...interface{}) {
	control.dbController.Log(logLevel, format, args...)
}

func (control *RunningController) Close(locks controller.Locks) {
	locks.LockController()
	control.mu.Lock()
	control.closed = true
	control.setState(StateStopped)
	control.mu.Unlock()

	control.stateSender.Wait()
	control.enc.Close()
}

func (control *RunningController) apiTaskSendState(nodeTask *common.DbNodeTask) error {
	var err error

	req, err := http.NewRequest("POST", control.apiUrl, bytes.NewBuffer(nodeTask.ApiState().Json()))
	if err != nil {
		return err
	}
	resp, err := control.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return err
		}

		return fmt.Errorf("%s returned status %s: %s", control.apiUrl, resp.Status, body)
	}

	return nil
}

func (control *RunningController) confirm(nodeTask *common.DbNodeTask) error {
	return control.enc.Publish(fmt.Sprintf("task_confirmation.%s", mNats.EscapeRoutingKey(nodeTask.Id)), nil)
}

type AsyncFunction struct {
	F    interface{}
	Args []interface{}
}

func (control *RunningController) asyncHandler(ch <-chan AsyncFunction) {
	var err error

	for fns := range ch {
		f := reflect.ValueOf(fns.F)
		if f.Type().NumIn() != len(fns.Args) {
			panic("asyncHandler: incorrect number of parameters")
		}
		params := make([]reflect.Value, len(fns.Args))
		for k, in := range fns.Args {
			params[k] = reflect.ValueOf(in)
		}
		ret := f.Call(params)[0]
		errI := ret.Interface()
		if errI != nil {
			err = errI.(error)
			fName := runtime.FuncForPC(f.Pointer()).Name()
			control.Log(log.LOG_ERROR, "asyncHandler '%s' ERROR: %s", fName, err)
		}
	}

	panic("channel in asyncHandler closed") // TODO name of the channel
}

func (control *RunningController) asyncQueue(ch chan<- AsyncFunction, f interface{}, args ...interface{}) error {
	af := AsyncFunction{
		F:    f,
		Args: append(make([]interface{}, 0, len(args)), args...),
	}
	select {
	case ch <- af:
	default:
		return errors.New("async queue full")
	}
	return nil
}

func (control *RunningController) queueDBQuery(f interface{}, args ...interface{}) error {
	if control.state(StateMaster) && control.Db != nil && control.Db.Connected() {
		return control.asyncQueue(control.dbQueries, f, args...)
	}
	return nil
}

func (control *RunningController) State(state string, locks controller.Locks) bool {
	locks.RLockController()
	control.mu.RLock()
	defer control.mu.RUnlock()
	defer locks.RUnlockController()

	return control.state(state)
}

func (control *RunningController) state(state string) bool {
	return control.dbController.State_ == state
}

func (control *RunningController) setState(state string) {
	if state == control.dbController.State_ {
		return
	}
	control.Log(log.LOG_WARN, "state: %s -> %s", control.dbController.State_, state)
	control.dbController.State_ = state

	control.stateSender.StateBump()
}

func (control *RunningController) SetState(state string, locks controller.Locks) {
	locks.LockController()
	control.mu.Lock()
	defer control.mu.Unlock()
	defer locks.UnlockController()

	control.setState(state)
}

func (control *RunningController) getState() (interface{}, bool) {
	locks := controller.NewLocks() // called only from stateSender
	now := time.Now()

	control.dbController.HasDBConnection = control.DbConnected(locks)
	control.dbController.LastOnline = now

	locks.RLockController()
	control.mu.RLock()
	defer control.mu.RUnlock()
	defer locks.RUnlockController()

	if control.dbController.State_ == StateMaster {
		control.dbController.LastMaster = &now
	}

	return control.dbController, control.state(StateStopped)
}

func (control *RunningController) DbConnected(locks controller.Locks) bool {
	if control.Db == nil {
		return false
	}
	if !control.Db.Connected() {
		return false
	}

	locks.RLockController()
	control.mu.RLock()
	defer control.mu.RUnlock()
	defer locks.RUnlockController()

	return time.Since(control.lastDbWrite) <= 1*time.Minute
}

func (control *RunningController) dbHandleRunningControllers(locks controller.Locks) error {
	return nil // TODO
}

func (control *RunningController) dbHandleNodes(locks controller.Locks) error {
	rows, err := control.Db.SelectNodes()
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var capabilities *string
		var dbNode common.DbNode
		err = rows.Scan(&dbNode.Id, &capabilities, &dbNode.State, &dbNode.Enabled)
		if err != nil {
			return err
		}
		node := control.cloud.GetNode(dbNode.Id, locks)
		if node != nil {
			if capabilities != nil {
				par := &common.Flags{}
				err = gocommon.JsonParseString(*capabilities, &par)
				if err != nil {
					control.Log(log.LOG_ERROR, "ERROR: failed to parse Capabilities in Node '%s': %s", dbNode.Id, err)
					continue // TODO handle?
				}
				dbNode.Capabilities = par
			}
			control.cloud.UpdateNode(&dbNode, locks)
		} else {
			if control.State(StateMaster, locks) {
				err = control.Db.CleanNode(&dbNode) // TODO do not replace "stopped" state with "unknown"
				if err != nil {
					control.Log(log.LOG_ERROR, "ERROR: %s")
				}
			}
		}
	}
	if rows.Err() != nil {
		return rows.Err()
	}
	return nil
}

func (control *RunningController) dbHandleNodeTasks(locks controller.Locks) error {
	if !control.State(StateMaster, locks) {
		return nil
	}
	rows, err := control.Db.SelectNodeTasks()
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var nodeTask common.DbNodeTask
		var startTime *string
		var lastOnlineTime *string

		err = rows.Scan(&nodeTask.Task, &nodeTask.Node, &nodeTask.Revision, &lastOnlineTime, &startTime)
		if err != nil {
			control.Log(log.LOG_ERROR, "SelectNodeTasks: error in rows.Scan(): %s", err)
		}
		if lastOnlineTime != nil {
			lastOnlineTimeTime, err := time.Parse("2006-01-02 15:04:05", *lastOnlineTime)
			if err == nil {
				nodeTask.LastOnlineTime = &lastOnlineTimeTime
			}
		}
		if nodeTask.LastOnlineTime != nil && time.Since(*nodeTask.LastOnlineTime).Minutes() > 2 {
			err = control.Db.DeleteNodeTask(&nodeTask)
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR :%s", err)
			}
		}
	}
	if rows.Err() != nil {
		return rows.Err()
	}
	return nil
}

func (control *RunningController) dbHandleTasks(locks controller.Locks) error {
	knownDbTasks := make(map[int]*common.DbTask)
	for task := range control.cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
		dbTask := task.DbTask()
		knownDbTasks[dbTask.DbId] = dbTask
	}

	rows, err := control.Db.SelectTasks()
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var requirementsJson *string
		var requiredResourcesJson *string
		var newTask common.DbTask

		err = rows.Scan(&newTask.DbId, &newTask.Id, &newTask.Alternative, &newTask.Order, &newTask.Level, &newTask.Resource, &newTask.ResourceCode, &newTask.Revision, &newTask.Class, &newTask.Finite, &newTask.Action, &newTask.Priority, &newTask.Parameters, &newTask.Creation, &requirementsJson, &requiredResourcesJson, &newTask.Comment, &newTask.DependsOn, &newTask.After, &newTask.RequiresFinishedDeps, &newTask.PrivateData)
		if err != nil {
			return err
		}
		delete(knownDbTasks, newTask.DbId)

		newTask.Requirements = &common.Flags{}
		if requirementsJson != nil {
			err = gocommon.JsonParseString(*requirementsJson, &newTask.Requirements)
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR: failed to parse Requirements in Task '%s': %s", newTask.Id, err)
				continue
			}
		}
		newTask.RequiredResources = &common.Resources{}
		if requiredResourcesJson != nil {
			err = gocommon.JsonParseString(*requiredResourcesJson, &newTask.RequiredResources)
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR: failed to parse RequiredResources in Task '%s': %s", newTask.Id, err)
				continue
			}
		}

		task := control.AddTask(&newTask)

		if task.DbTask().RemovalReady() && control.State(StateMaster, locks) {
			_ = control.RemoveTask(&newTask, locks)
		}
	}
	if rows.Err() != nil {
		return rows.Err()
	}

	for _, unknownDbTask := range knownDbTasks {
		if control.State(StateMaster, locks) {
			unknownDbTask.Log(log.LOG_WARN, "task %s not found in the database, terminating", unknownDbTask.Id)
			control.cloud.TerminateTask(unknownDbTask, locks)
			controller.AssertLock(locks.Task, controller.LockUnlocked)
		}
		_ = control.cloud.RemoveTask(unknownDbTask, locks)
	}

	if control.State(StateMaster, locks) {
		err = control.sendStoredDirectories(locks) // TODO handle error
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR sending stored locations: %s", err)
			err = nil
		}
	}

	control.Log(log.LOG_DEBUG, "db update done")
	control.cloud.HandleTasks()

	return err
}

func (control *RunningController) DBBump() {
	select {
	case control.dbBump <- true:
	default:
	}
}

func (control *RunningController) dbUpdater(locks controller.Locks) {
	if control.Db == nil {
		return
	}
	control.Db.Connect()

	var err error
	var bumped bool
	ticker := time.NewTicker(controller.DatabaseUpdateTime)

	for {
		select {
		case <-ticker.C:
		case <-control.dbBump:
			bumped = true
		}
		if !control.Db.Connected() {
			control.DBBump()
		} else {
			control.Log(log.LOG_DEBUG, "updating from the database")

			/* RunningControllers */
			err = control.dbHandleRunningControllers(locks)
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR: %s", err)
				control.DBBump()
			}

			/* OriginNodes */
			err = control.dbHandleNodes(locks)
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR: %s", err)
				control.DBBump()
			}

			/* NodeTasks */
			err = control.dbHandleNodeTasks(locks)
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR: %s", err)
				control.DBBump()
			}

			/* Tasks */
			err = control.dbHandleTasks(locks)
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR: %s", err)
				control.DBBump()
			}
		}
		if bumped {
			time.Sleep(5 * time.Second)
			bumped = false
		}
	}
}

func (control *RunningController) FinishedFiniteTask(nodeTask *common.DbNodeTask, locks controller.Locks) {
	locks.LockController()
	control.mu.Lock()
	defer control.mu.Unlock()
	defer locks.UnlockController()

	if !control.state(StateMaster) {
		return
	}

	if control.finishedTasksSet[nodeTask.Id] {
		// we are already handling this nodeTask
		return
	}
	control.finishedTasksSet[nodeTask.Id] = true

	go func() {
		locks := controller.NewLocks()

		defer func() {
			locks.LockController()
			control.mu.Lock()
			delete(control.finishedTasksSet, nodeTask.Id)
			locks.UnlockController()
			control.mu.Unlock()
		}()

		var err error

		control.Log(log.LOG_DEBUG, "processing finished node task %s", nodeTask.Id)
		err = control.apiTaskSendState(nodeTask)
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR in api call for %s: %s", nodeTask.Id, err)
			return
		}

		if nodeTask.State == "done" {
			err = control.Db.DoneTask(&nodeTask.OriginTask.DbTask) // TODO
			if err != nil {
				control.Log(log.LOG_ERROR, "ERROR terminating done task: %s", err)
				return
			}
			control.cloud.DoneTask(&nodeTask.OriginTask.DbTask, locks) // TODO
		}

		err = control.confirm(nodeTask)
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR in confirm(%s): %s", nodeTask.Id, err)
			return
		}

		control.Log(log.LOG_INFO, "node task done: %s", nodeTask.Id)
	}()
}

func (control *RunningController) AddTask(dbTask *common.DbTask) *task.Task {
	locks := controller.NewLocks()
	return control.cloud.AddTask(dbTask, false, locks)
}

func (control *RunningController) RemoveTask(dbTask *common.DbTask, locks controller.Locks) bool {
	locks.LockController()
	control.mu.Lock()
	defer control.mu.Unlock()
	defer locks.UnlockController()

	removed := control.cloud.RemoveTask(dbTask, locks)
	if removed {
		_ = control.queueDBQuery(control.Db.DeleteTask, dbTask)
	}

	return removed
}

func (control *RunningController) RemoveNodeTask(dbNodeTask *common.DbNodeTask, locks controller.Locks) bool {
	removed := true // TODO
	control.cloud.RemoveNodeTask(dbNodeTask, locks)

	if removed { // nolint:staticcheck
		//_ = self.queueDBQuery(self.Db.DeleteNodeTask, dbNodeTask)
	}
	return removed
}

func (control *RunningController) sendStoredDirectories(locks controller.Locks) error {
	type StorageSpecification struct { // TODO remove
		Command string
		Vod     bool
		Url     string
		Storage string
	}

	var err error

	if !control.State(StateMaster, locks) {
		return nil
	}

	var storedDirectories []storage.StoredDirectory
	for task := range control.cloud.TaskIterator(func(task *task.Task) bool {
		dbTask := task.DbTask()
		return dbTask.Class != nil && *dbTask.Class == "storage"
	}, locks) {
		dbTask := task.DbTask()
		if dbTask.Parameters == nil {
			continue
		}

		var specification common.Specification
		err = json.Unmarshal([]byte(*dbTask.Parameters), &specification)
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR: %s", err)
			continue
		}

		var storageSpecification StorageSpecification
		err = json.Unmarshal([]byte(specification["start"]), &storageSpecification) // TODO remove
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR: %s", err)
			continue
		}

		parsedUrl, err := url.Parse(storageSpecification.Url)
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR: %s", err)
			continue
		}

		storedDirectories = append(storedDirectories, storage.StoredDirectory{
			Path: filepath.Dir(parsedUrl.Path),
		})
	}

	err = control.enc.Publish("stored_directories", storedDirectories)
	if err != nil {
		return err
	}

	return nil
}

func (control *RunningController) statusReceiver(locks controller.Locks) {
	subNodeState, _ := control.enc.Subscribe("node_state.*", func(dbNode common.DbNode) {
		defer control.metrics.AddReceiveNatsMessage("node_state", time.Now())
		// self.Log(log.LOG_DEBUG, "node '%s' state: %s", dbNode.Id, dbNode.State)
		locks := controller.NewLocks()
		node := control.cloud.GetNode(dbNode.Id, locks)
		var oldState string
		if node != nil {
			oldState = node.State
		}
		if dbNode.State == "stopped" {
			if node != nil {
				control.cloud.RemoveNode(node, locks)
			}
			control.Log(log.LOG_INFO, "node stopped: %s", dbNode.Id)
		} else {
			_, added := control.cloud.AddNode(&dbNode, locks)
			locks.AssertUnlocked()
			if added {
				control.Log(log.LOG_INFO, "new node %s (group: %s)", dbNode.Id, dbNode.Group)
				control.DBBump()
			}
			if dbNode.State == "running" && oldState != "running" {
				control.cloud.HandleTasks()
			}
		}
	})
	control.metrics.AddNatsSubscriptionPending("node_state", subNodeState.Pending)

	subTaskState, _ := control.enc.Conn.SubscribeSync("task_state.*")
	control.metrics.AddNatsSubscriptionPending("task_state", subTaskState.Pending)

	// multiple workers for task_state.* messages
	for i := 0; i < control.workersForTasks; i++ {
		go func() {
			for {
				msg, err := subTaskState.NextMsg(1 * time.Second)
				if err != nil {
					continue
				}
				// for dbNodeTask := range taskStateChan {
				start := time.Now()

				var dbNodeTask common.DbNodeTask
				err = json.Unmarshal(msg.Data, &dbNodeTask)
				if err != nil {
					control.Log(log.LOG_INFO, "failed to parse task_state message")
					continue
				}

				locks := controller.NewLocks()
				// self.Log(log.LOG_DEBUG, "NodeTask '%s': %s", dbNodeTask.Id, dbNodeTask.State)
				control.cloud.AddNodeTask(&dbNodeTask, locks)
				locks.AssertUnlocked()

				if dbNodeTask.OriginTask.Finite && (dbNodeTask.State == "done" || dbNodeTask.State == "failed") {
					control.FinishedFiniteTask(&dbNodeTask, locks)
				} else if dbNodeTask.State == "deleted" { // TODO use constant STATE_DELETED from task.go
					dbNodeTask.OriginTask.Log(log.LOG_DEBUG, "nodeTask deleted '%s'", dbNodeTask.Id)
					control.cloud.HandleTasks()
				}
				control.metrics.AddReceiveNatsMessage("task_state", start)
			}
		}()
	}

	recvTimer := time.AfterFunc(common.ControllerStateInterval*3, func() {
		locks := controller.NewLocks()
		control.Log(log.LOG_WARN, "not receiving our own state")
		locks.LockController()
		control.mu.Lock()
		if control.state(StateIdle) || control.state(StateMaster) {
			control.setState(StateError)
		}
		locks.UnlockController()
		control.mu.Unlock()
	})
	defer recvTimer.Stop()
	lastMasterTime := time.Now()
	control.SetState(StateIdle, locks)

	subControllerState, _ := control.enc.Subscribe("controller_state.*", func(dbController common.DbController) {
		locks := controller.NewLocks()

		defer control.metrics.AddReceiveNatsMessage("controller_state", time.Now())
		added := control.cloud.AddController(&dbController)
		if added {
			control.Log(log.LOG_INFO, "new controller %s", dbController.Id)
		}

		// reset receive check timer
		if dbController.Id == control.dbController.Id {
			_ = recvTimer.Reset(common.ControllerStateInterval * 3)

			locks.LockController()
			control.mu.Lock()
			if control.state(StateError) {
				control.setState(StateIdle)
			}
			locks.UnlockController()
			control.mu.Unlock()
		}

		if dbController.State_ == StateMaster {
			lastMasterTime = time.Now()
		}

		// self.LogDebug("[%v] controller '%v': %s", q.Name, controller.Id, controller.State_)
		if control.State(StateMaster, locks) {
			dbConnected := control.DbConnected(locks)
			if !dbConnected && dbController.HasDBConnection {
				control.Log(log.LOG_WARN, "controller '%s' has db connection, dropping master", dbController.Id)
				control.SetState(StateIdle, locks)
			}
			if dbController.State_ == StateMaster {
				// self.LogDebug("[%v] controller '%v' state: %s", q.Name, controller.Id, controller.State)
				if dbController.Id != control.dbController.Id {
					if dbController.Priority > control.dbController.Priority && dbController.HasDBConnection == dbConnected {
						control.Log(log.LOG_WARN, "master controller '%s' has higher priority, dropping master", dbController.Id)
						control.SetState(StateIdle, locks)
					} else if dbController.Priority == control.dbController.Priority && dbController.HasDBConnection == dbConnected && dbController.LastMaster.Before(*control.dbController.LastMaster) {
						control.Log(log.LOG_WARN, "master controller '%s' was first, dropping master", dbController.Id)
						control.SetState(StateIdle, locks)
					} else {
						control.Log(log.LOG_WARN, "controller '%s' has state: %s", dbController.Id, dbController.State_)
					}
				}
			}
		}

		if control.State(StateIdle, locks) && control.Quorum(locks) && time.Since(lastMasterTime) > common.ControllerStateInterval*4 {
			control.Log(log.LOG_DEBUG, "no master controller, taking control")
			control.SetState(StateMaster, locks)
			control.DBBump()
		}
	})
	control.metrics.AddNatsSubscriptionPending("controller_state", subControllerState.Pending)

	subApiTasks, _ := control.enc.Subscribe("api.tasks_updated", func(_ interface{}) {
		defer control.metrics.AddReceiveNatsMessage("api_tasks", time.Now())
		control.Log(log.LOG_DEBUG, "received task db updated message")
		control.DBBump()
	})
	control.metrics.AddNatsSubscriptionPending("api_tasks", subApiTasks.Pending)

	subStoredDiectories, _ := control.enc.Subscribe("request.controller.stored_directories", func(_ interface{}) {
		locks := controller.NewLocks()
		defer control.metrics.AddReceiveNatsMessage("stored_directories", time.Now())
		control.Log(log.LOG_DEBUG, "received request_stored_directories")
		if !control.State(StateMaster, locks) {
			return
		}

		err := control.sendStoredDirectories(locks)
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR sending stored locations: %s", err)
		}
	})
	control.metrics.AddNatsSubscriptionPending("stored_directories", subStoredDiectories.Pending)
}

func (control *RunningController) prioritize(priorityParam int) {
	locks := controller.NewLocks() // method called only as goroutine
	if priorityParam != AutoPriority {
		control.dbController.Priority = priorityParam
		return
	}

	var filled bool
	var err error
	numCPU := float64(runtime.NumCPU())
	ticker := time.NewTicker(controller.ReprioritizeDuration)
	defer ticker.Stop()
	for {
		var free float64
		filled, err = control.cpuTotalCounter.Filled()
		if err == nil && filled {
			totalDelta, _ := control.cpuTotalCounter.GetTotal(30)
			idleDelta, _ := control.cpuIdleCounter.GetTotal(30)
			if totalDelta > 0 {
				free = float64(idleDelta) / float64(totalDelta)
			}
			free *= numCPU * 100
		}

		locks.LockController()
		control.mu.Lock()
		control.dbController.Priority = int(free)
		locks.UnlockController()
		control.mu.Unlock()

		<-ticker.C
	}
}

func (control *RunningController) Quorum(locks controller.Locks) (quorum bool) {
	locks.LockController()
	control.mu.Lock()
	quorum = control.quorum
	locks.UnlockController()
	control.mu.Unlock()

	return
}

func (control *RunningController) quorumMonitor(quorum int) {
	locks := controller.NewLocks()
	ticker := time.NewTicker(controller.QuorumCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		numControllers := len(control.cloud.GetControllers())
		if numControllers >= quorum {
			locks.LockController()
			control.mu.Lock()
			control.quorum = true
			locks.UnlockController()
			control.mu.Unlock()
			continue
		}

		control.Log(log.LOG_WARN, "only %d out of %d required controllers visible", numControllers, quorum)

		locks.LockController()
		control.mu.Lock()
		if control.state(StateMaster) {
			control.setState(StateIdle)
		}
		control.quorum = false
		locks.UnlockController()
		control.mu.Unlock()
	}
}

func (control *RunningController) dispatcher() {
	locks := controller.NewLocks()
	var err error
	for taskCommand := range control.cloud.TaskDispatchQueue {
		if !control.State(StateMaster, locks) {
			continue
		}

		err = control.dispatch(taskCommand)
		if err != nil {
			taskCommand.Task.Log(log.LOG_WARN, "failed to publish task command: %s", err)
		}

		taskCommand.Task.Log(log.LOG_INFO, "dispatched task command for '%s': %s @ '%s'", taskCommand.Task.Id, taskCommand.Task.Action, taskCommand.Destination)
	}
}

func (control *RunningController) dispatch(taskCommand cloud.TaskCommand) (err error) {
	locks := controller.NewLocks()
	if taskCommand.Task.Action == "wait" {
		return
	}

	if taskCommand.Task.Action == "run" {
		if control.cloud.IsTaskIdRunning(taskCommand.Task.Id, 0, locks) {
			return
		}
		err = control.enc.Publish(fmt.Sprintf("node_command.%s", taskCommand.Destination), taskCommand.Task)
		if err != nil {
			return
		}
	} else {
		err = control.enc.Publish(fmt.Sprintf("task_command.%s", taskCommand.Task.Id), taskCommand.Task.Action)
		if err != nil {
			return
		}

		// TODO remove
		err = control.enc.Publish(fmt.Sprintf("node_command.%s", taskCommand.Destination), taskCommand.Task)
		if err != nil {
			return
		}
	}

	return
}

func (control *RunningController) DbPush(locks controller.Locks) error {
	var err error

	var tx *sql.Tx
	tx, err = control.Db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()

			// In case the tx fails because there is a nodetask
			// for a Task that no longer exists in the db
			control.DBBump()
			return
		}
	}()

	nodes := control.cloud.GetNodes(locks)
	var nodeTasks []*common.DbNodeTask
	for _, node := range nodes {
		nodeTasks = append(nodeTasks, node.GetNodeTaskList(locks)...)

		dbNode := node.GetDbNode(locks)
		if dbNode.Host == "<zombie>" {
			continue
		}

		dbNode.AvailableResources = common.NewMultiResources()
		for res := range dbNode.FreeResources {
			dbNode.AvailableResources[res], _ = node.GetAvailableResource(res, locks)
		}

		err = control.Db.InsertNode(tx, &dbNode)
		if err != nil {
			return err
		}
	}

	for _, nodeTask := range nodeTasks {
		if nodeTask.State == "dummy" {
			continue
		}

		task := control.cloud.GetTaskDb(&nodeTask.OriginTask.DbTask, locks)
		if task == nil {
			continue
		}
		if task.Zombie(locks) {
			continue
		}
		err = control.Db.InsertNodeTask(tx, nodeTask)
		if err != nil {
			return err
		}
		if nodeTask.State == "deleted" {
			control.RemoveNodeTask(nodeTask, locks)
			task := control.cloud.GetTaskDb(&nodeTask.OriginTask.DbTask, locks)
			if task != nil && task.DbTask().RemovalReady() {
				control.RemoveTask(&nodeTask.OriginTask.DbTask, locks)
			}
		}
	}

	go func() {
		err := control.dbPushTaskPrivateData(nodeTasks)
		if err != nil {
			control.Log(log.LOG_ERROR, "ERROR: %s", err)
		}
	}()

	if !controller.SelfControllerInsert {
		controllers := control.cloud.GetControllers()
		for _, controller := range controllers {
			err = control.Db.InsertController(tx, controller.DbController)
			if err != nil {
				return err
			}
		}
	}

	return tx.Commit()
}

func (control *RunningController) DbPushSelf() (err error) {
	if !controller.SelfControllerInsert {
		return
	}

	var tx *sql.Tx
	tx, err = control.Db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	err = control.Db.InsertController(tx, control.dbController)
	if err != nil {
		return err
	}

	return tx.Commit()
}

func (control *RunningController) dbPushTaskPrivateData(nodeTasks []*common.DbNodeTask) error {
	var err error
	var tx *sql.Tx

	tx, err = control.Db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	for _, nodeTask := range nodeTasks {
		if nodeTask.PrivateData != nil {
			err = control.Db.StoreTaskData(tx, &nodeTask.OriginTask.DbTask, *nodeTask.PrivateData)
			if err != nil {
				return err
			}
		}
	}

	return tx.Commit()
}
