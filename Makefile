VERSION=$(shell git describe --tags --dirty | sed -e 's/^v//')
GOVERSION=$(shell go version | cut -d " " -f3 | cut -d "." -f1,2 | tr '.' '_')
LDFLAGS=-ldflags "-X main.VERSION=$(VERSION)"
TAGS=production

all: build
build: build-controller build-node build-task build-debugger build-gpu-info
build-controller:
	CGO_ENABLED=0 go build -tags "$(TAGS)" $(LDFLAGS) -o bin/controller ./cmd/controller
build-node:
	CGO_ENABLED=0 go build -tags "$(TAGS)" $(LDFLAGS) -o bin/node ./cmd/node
build-gpu-info:
	CGO_ENABLED=0 go build $(LDFLAGS) -o bin/gpu-info ./cmd/gpu-info
build-task: task/yaegi_dep/$(GOVERSION)_yaegi_dep.go
	go build -tags "$(TAGS)" $(LDFLAGS) -o bin/task ./cmd/task
build-debugger:
	go build -tags "$(TAGS)" $(LDFLAGS) -o bin/debugger ./cmd/debugger
build-runcheck:
	go build -tags "$(TAGS)" $(LDFLAGS) -o bin/runcheck ./cmd/runcheck

build-dummy-controller:
	CGO_ENABLED=0 go build -tags "$(TAGS)" $(LDFLAGS) -o bin/dummy-controller ./cmd/dummy-controller

test: task/yaegi_dep/$(GOVERSION)_yaegi_dep.go
	go test -cover -timeout 120s ./...

task/yaegi_dep/$(GOVERSION)_yaegi_dep.go:
	rm -rf yaegi_dep
	mkdir yaegi_dep
	cp task/yaegi_dep.go.common yaegi_dep/$(GOVERSION)_yaegi_dep.go
	cd yaegi_dep && go run github.com/traefik/yaegi/cmd/yaegi extract git.moderntv.eu/go/common
	cd yaegi_dep && go run github.com/traefik/yaegi/cmd/yaegi extract git.moderntv.eu/go/common/file
	cd yaegi_dep && go run github.com/traefik/yaegi/cmd/yaegi extract git.moderntv.eu/mcloud/classes
	cd yaegi_dep && go run github.com/traefik/yaegi/cmd/yaegi extract git.moderntv.eu/mcloud/storage/data
	cd yaegi_dep && go run github.com/traefik/yaegi/cmd/yaegi extract git.moderntv.eu/mcloud/classes/pkg/hls
	rm -rf task/yaegi_dep
	mv yaegi_dep task/

.PHONY: lint
lint: task/yaegi_dep/$(GOVERSION)_yaegi_dep.go
	go run github.com/golangci/golangci-lint/cmd/golangci-lint@v1.61.0 run

clean:
	rm -rf bin/controller bin/node bin/task ./bin/debugger yaegi_dep task/yaegi_dep

.PHONY: all test test-short clean build-controller build-node build-task build-debugger
